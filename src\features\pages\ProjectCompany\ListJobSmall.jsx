import React, { useState, useEffect } from 'react';
import startIcon from '../../../assets/start.svg';
import doneIcon from '../../../assets/done.svg';
import undoIcon from '../../../assets/undo.svg';
import userIcon from '../../../assets/user.svg';
import alarmClockPlusIcon from '../../../assets/alarm-clock-plus.svg';
import trashIcon from '../../../assets/trash.svg';
import deploymentIcon from '../../../assets/deployment.svg';
import completeIcon from '../../../assets/complete.svg';
import waitingIcon from '../../../assets/waiting.svg';
import overdueIcon from '../../../assets/triangle-alert.svg';
import considerIcon from '../../../assets/consider.svg';
import timeredIcon from '../../../assets/timered.svg';
  // Lấy icon trạng thái giống Big
  const getStatusIcon = (status) => {
    switch (status) {
      case 'Đang triển khai':
        return deploymentIcon;
      case 'Hoàn thành':
        return completeIcon;
      case 'Đang chờ':
        return waitingIcon;
      case 'Chờ điều kiện tiên quyết':
        return timeredIcon;
      case 'Quá hạn':
        return overdueIcon;
      case 'Xem xét':
        return considerIcon;
      default:
        return waitingIcon;
    }
  };
import '../../../styles/ListJobSmall.css';
import { 
  getAllSmallTasks, 
  updateSmallTaskStatus, 
  updateSmallTaskTime,
  deleteSmallTask
} from '../../../storage/smallTasksData';

const ListJobSmall = ({ task, onTaskUpdate, onTaskDelete }) => {
  const [currentTask, setCurrentTask] = useState(task);
  
  // Lọc các nhiệm vụ phụ thuộc hợp lệ (không rỗng, không null)
  // Kiểm tra cả dependencies array và dependentTaskId object
  let validDependencies = [];
  
  if (Array.isArray(currentTask.dependencies)) {
    validDependencies = currentTask.dependencies.filter(dep => dep && dep.trim() !== "");
  } else if (currentTask.dependentTaskId) {
    // Nếu có dependentTaskId nhưng không có dependencies array, tạo dependencies từ dependentTaskId
    if (typeof currentTask.dependentTaskId === 'object' && currentTask.dependentTaskId.taskCode && currentTask.dependentTaskId.title) {
      validDependencies = [`${currentTask.dependentTaskId.taskCode} ${currentTask.dependentTaskId.title}`];
    } else if (typeof currentTask.dependentTaskId === 'string') {
      validDependencies = [currentTask.dependentTaskId];
    }
  }

  useEffect(() => {
    setCurrentTask(task);
  }, [task]);

  // Clear localStorage on mount to ensure fresh data
  useEffect(() => {
    localStorage.removeItem('small_tasks_data');
  }, []);

  const handleStartTask = () => {
    // Check if this is a hardcoded task or API task
    if (currentTask.id && currentTask.id.startsWith('small-')) {
      // Hardcoded task
      const updatedTask = updateSmallTaskStatus(currentTask.id, 'Đang triển khai');
      setCurrentTask(updatedTask.find(t => t.id === currentTask.id));
      if (onTaskUpdate) {
        onTaskUpdate(updatedTask);
      }
    } else {
      // API task
      const newStatus = 'Đang triển khai';
      setCurrentTask({...currentTask, status: newStatus});
      if (onTaskUpdate) {
        onTaskUpdate(newStatus);
      }
    }
  };

  const handleStatusChange = (newStatus) => {
    // Check if this is a hardcoded task or API task
    if (currentTask.id && currentTask.id.startsWith('small-')) {
      // Hardcoded task
      const updatedTask = updateSmallTaskStatus(currentTask.id, newStatus);
      setCurrentTask(updatedTask.find(t => t.id === currentTask.id));
      if (onTaskUpdate) {
        onTaskUpdate(updatedTask);
      }
    } else {
      // API task
      setCurrentTask({...currentTask, status: newStatus});
      if (onTaskUpdate) {
        onTaskUpdate(newStatus);
      }
    }
  };

  const handleDelete = () => {
    if (window.confirm('Bạn có chắc chắn muốn xóa nhiệm vụ này?')) {
      // Check if this is a hardcoded task or API task
      if (currentTask.id && currentTask.id.startsWith('small-')) {
        // Hardcoded task
        const updatedTasks = deleteSmallTask(currentTask.id);
        if (onTaskDelete) {
          onTaskDelete(updatedTasks);
        }
      } else {
        // API task
        if (onTaskDelete) {
          onTaskDelete();
        }
      }
    }
  };

  return (
    <div className="task-card">
      {/* Header Section */}
      <div className="task-header">
        <div className="task-title-section">
          <span className="task-category">{currentTask.category}</span>
          <span className="task-status">
            {currentTask.status === 'Đang chờ' && validDependencies.length > 0 ? (
              <span className="status-badge status-waiting-dependency">
                <img src={timeredIcon} alt="status" className="status-icon" />
                Chờ điều kiện tiên quyết
              </span>
            ) : (
              <span className="status-badge">
                <img src={getStatusIcon(currentTask.status)} alt="status" className="status-icon" />
                {currentTask.status}
              </span>
            )}
          </span>
        </div>
        <div className="task-actions">
          {currentTask.status === 'Đang chờ' && (
            validDependencies.length > 0 ? (
              <button className="action-button waiting-button" disabled>
                Đang chờ
              </button>
            ) : (
              <button className="action-button start-button" onClick={handleStartTask}>
                <img src={startIcon} alt="start" className="button-icon" />
                Bắt đầu
              </button>
            )
          )}
          {currentTask.status === 'Đang triển khai' && (
            <button className="action-button complete-button" onClick={() => handleStatusChange('Hoàn thành')}>
              <img src={doneIcon} alt="done" className="button-icon" />
              Hoàn thành
            </button>
          )}
          {currentTask.status === 'Hoàn thành' && (
            <button className="action-button undo-button" onClick={() => handleStatusChange('Đang triển khai')}>
              <img src={undoIcon} alt="undo" className="button-icon" />
              Hoàn tác
            </button>
          )}
          {/* Delete button for API tasks */}
          {currentTask.id && !currentTask.id.startsWith('small-') && (
            <button className="action-button delete-button" onClick={handleDelete}>
              <img src={trashIcon} alt="delete" className="button-icon" />
              Xóa
            </button>
          )}
        </div>
      </div>

      {/* Task Title */}
      <div className="task-title" style={{ fontWeight: 'bold', marginBottom: '12px' }}>
        {currentTask.title || currentTask.description}
      </div>

      {/* Task Info Section */}
      <div className="task-info">
        {/* Assignee Section */}
        <div className="info-row">
          <div className="info-label">
            <img src={userIcon} alt="user" className="info-icon" />
            Người thực hiện
          </div>
          <div className="assignee-info">
            <div className="assignee-avatar">
              <span className="avatar-text">{currentTask.assignee.name.charAt(0)}</span>
            </div>
            <span className="assignee-name">{currentTask.assignee.name}</span>
          </div>
        </div>

        {/* Start Date */}
        <div className="info-row">
          <div className="info-label">
            <svg className="info-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
            </svg>
            Ngày bắt đầu
          </div>
          <div className="time-info">
            <span className="time-value">{currentTask.startDate || 'Không có'}</span>
          </div>
        </div>

        {/* Due Date */}
        <div className="info-row">
          <div className="info-label">
            <svg className="info-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
            </svg>
            Ngày kết thúc
          </div>
          <div className="time-info">
            <span className="time-value">{currentTask.dueDate || 'Không có'}</span>
          </div>
        </div>

        {/* Estimated Time */}
        <div className="info-row">
          <div className="info-label">
            <svg className="info-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z"/>
              <path d="M12.5 7H11v6l5.25 3.15.75-1.23-4.5-2.67z"/>
            </svg>
            Thời gian dự kiến
          </div>
          <div className="time-info">
            <span className="time-value">{currentTask.estimatedTime || 'Không có'}</span>
          </div>
        </div>

        {/* Priority */}
        <div className="info-row">
          <div className="info-label">
            <svg className="info-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
            Mức độ ưu tiên
          </div>
          <div className="priority-info">
            <span className={`priority-value priority-${currentTask.priority || 'medium'}`}>
              {currentTask.priority === 'high' ? 'Cao' : 
               currentTask.priority === 'medium' ? 'Trung bình' : 
               currentTask.priority === 'low' ? 'Thấp' : 
               currentTask.priority === 'critical' ? 'Khẩn cấp' : 'Trung bình'}
            </span>
          </div>
        </div>

        {/* Description */}
        <div className="info-row">
          <div className="info-label">
            <svg className="info-icon" viewBox="0 0 24 24" fill="currentColor">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
            </svg>
            Mô tả
          </div>
          <div className="description-info">
            <span className="description-text">
              {currentTask.description && currentTask.description !== currentTask.title 
                ? currentTask.description 
                : 'Không có'}
            </span>
          </div>
        </div>


      </div>

      {/* Dependencies Section */}
      <div className="task-dependencies">
        <div className="dependency-title">Phụ thuộc nhiệm vụ</div>
        <div className="dependency-content">
          <div className="dependency-row">
            <span className="dependency-label">Phụ thuộc vào</span>
            <div className="dependency-tags">
              {validDependencies.length > 0
                ? validDependencies.map((dep, idx) => (
                    <span key={idx} className="dependency-tag">{dep}</span>
                  ))
                : <span className="dependency-tag">Không có</span>
              }
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ListJobSmall;