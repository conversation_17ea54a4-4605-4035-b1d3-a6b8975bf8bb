// Import các thư viện và component cần thiết
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useLocation, useOutletContext, useParams } from "react-router-dom";
import "../../../styles/ListProject.css";

import DetailJob from "../../components/JobDetail";
import JobCreate from "../../components/JobCreate";
import ListJobSmall from "./ListJobSmall";

// Import toast functions
import { showError, showSuccess } from "../../../utils/toastUtils";

// Import các icon SVG trạng thái và mức độ ưu tiên
import highPriorityIcon from "../../../assets/High.svg";
import mediumPriorityIcon from "../../../assets/Medium.svg";
import NoJobIcon from "../../../assets/NoJob.svg";
import normalPriorityIcon from "../../../assets/Normal.svg";
import cmtIcon from "../../../assets/cmt.svg";
import completeIcon from "../../../assets/complete.svg";
import considerIcon from "../../../assets/consider.svg";
import deploymentIcon from "../../../assets/deployment.svg";
import overdueIcon from "../../../assets/triangle-alert.svg";
import detailIcon from "../../../assets/eye.svg";
import addIcon from "../../../assets/add.svg";
import trashIcon from "../../../assets/trash.svg";
import defaultAvatar from "../../../assets/user1.png";
import waitingIcon from "../../../assets/waiting.svg";
import arrowrightIcon from "../../../assets/icon-sidebar/arrow.svg";
import arrowbottomIcon from "../../../assets/icon-sidebar/dropdown.svg";

// Import API functions thay vì local storage
import {
    deleteProjectTask,
    getProjectTasks,
    transformTaskListData,
    updateProjectTask
} from "../../../api/taskManagement";
import {
    getProjectMembers,
    transformUserData,
} from "../../../api/userManagement";

// Thêm cache và preload ở đầu file
let taskListCache = {};
let cacheTimestamp = {};
const CACHE_DURATION = 10000; // 10 giây
let preloadPromiseMap = {};

// Component hiển thị danh sách công việc dạng bảng
const ListProject = ({
  sortOption: propSortOption,
  filterOptions: propFilterOptions,
}) => {
  // Lấy id dự án từ URL nếu có
  const { projectId } = useParams();
  // Lấy thông tin route hiện tại
  const location = useLocation();

  // Lấy context từ component cha (WorkContent) nếu có
  const context = useOutletContext() || {};

  // Ưu tiên props truyền vào, nếu không có thì lấy từ context
  const sortOption =
    propSortOption !== undefined ? propSortOption : context.sortBy;
  const filterOptions =
    propFilterOptions !== undefined ? propFilterOptions : context.filters;

  // Sử dụng taskList từ context nếu có, nếu không thì dùng state local
  const contextTaskList = context.taskList;
  const contextSetTaskList = context.setTaskList;

  // State quản lý task được chọn để xem chi tiết
  const [selectedTask, setSelectedTask] = useState(null);
  // State hiển thị popup đổi trạng thái
  const [showStatusPopup, setShowStatusPopup] = useState(false);
  // Vị trí popup trạng thái
  const [popupPosition, setPopupPosition] = useState({ top: 0, left: 0 });
  // Lưu id task đang thao tác đổi trạng thái
  const [activeTaskId, setActiveTaskId] = useState(null);
  // Lưu trạng thái đang hover
  const [hoveredStatus, setHoveredStatus] = useState(null);
  // State hiển thị popup đổi mức độ ưu tiên
  const [showPriorityPopup, setShowPriorityPopup] = useState(false);
  // Vị trí popup mức độ ưu tiên
  const [priorityPopupPosition, setPriorityPopupPosition] = useState({
    top: 0,
    left: 0,
  });
  // Lưu mức độ ưu tiên đang hover
  const [hoveredPriority, setHoveredPriority] = useState(null);
  // State quản lý menu hành động
  const [actionMenuPosition, setActionMenuPosition] = useState({ top: 0, left: 0 });
  const [showActionMenu, setShowActionMenu] = useState(false);
  const [selectedActionTask, setSelectedActionTask] = useState(null);
  // State quản lý form tạo nhiệm vụ phụ
  const [showSubTaskForm, setShowSubTaskForm] = useState(false);
  const [parentTaskForSubTask, setParentTaskForSubTask] = useState(null);
  // Danh sách công việc hiện tại - ưu tiên từ context
  const [localTaskList, setLocalTaskList] = useState([]);
  const taskList = contextTaskList || localTaskList;
  const setTaskList = contextSetTaskList || setLocalTaskList;
  // Loading state
  const [loading, setLoading] = useState(true);
  // Error state
  const [error, setError] = useState(null);
  // Lưu id task đang đổi trạng thái để hiệu ứng
  const [changingTaskId, setChangingTaskId] = useState(null);
  // Tooltip thành viên
  const [showAssigneeTooltip, setShowAssigneeTooltip] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const [tooltipContent, setTooltipContent] = useState("");
  // State quản lý các task được chọn (checkbox)
  const [selectedTaskIds, setSelectedTaskIds] = useState([]);

  const [projectMembers, setProjectMembers] = useState([]);
  const [loadingMembers, setLoadingMembers] = useState(false);

  // Removed hardcoded small tasks state

  // Removed hardcoded parent task - now using only API data

  // Lấy role của user hiện tại
  // const userRole = getUserRole();
  // const canSelectTasks = ['leader', 'departmenthead', 'hr', 'ceo', 'admin'].includes(userRole);
  const canSelectTasks = true;

  // Ref cho cell trạng thái, popup, tooltip
  const statusCellRef = useRef(null);
  const popupRef = useRef(null);
  const tooltipRef = useRef(null);
  // Ref cho cell mức độ ưu tiên và popup
  const priorityCellRef = useRef(null);
  const priorityPopupRef = useRef(null);

  // Kiểm tra có phải route team/tasks không
  const isTeamTasksRoute = location.pathname.startsWith("/team/tasks");

  // Preload function
  const preloadTasks = async (projectId) => {
    if (preloadPromiseMap[projectId]) return preloadPromiseMap[projectId];
    preloadPromiseMap[projectId] = (async () => {
      try {
        const response = await getProjectTasks(projectId);
        const transformedTasks = transformTaskListData(
          response.data || response
        );
        taskListCache[projectId] = transformedTasks;
        cacheTimestamp[projectId] = Date.now();
        return transformedTasks;
      } catch {
        return [];
      }
    })();
    return preloadPromiseMap[projectId];
  };

  // Load tasks từ API khi component mount hoặc projectId thay đổi (chỉ khi không có context)
  const loadTasks = useCallback(async () => {
    if (!projectId) return;
    if (contextTaskList) {
      setLoading(false); // Nếu có context taskList thì không cần loading
      return;
    }
    const now = Date.now();
    if (
      taskListCache[projectId] &&
      now - cacheTimestamp[projectId] < CACHE_DURATION
    ) {
      setLocalTaskList(taskListCache[projectId]);
      setLoading(false);
      return;
    }
    setLoading(true);
    let transformedTasks = [];
    if (preloadPromiseMap[projectId]) {
      transformedTasks = await preloadPromiseMap[projectId];
    } else {
      try {
        const response = await getProjectTasks(projectId);
        transformedTasks = transformTaskListData(response.data || response);
      } catch (err) {
        setError(err.message);
        setLocalTaskList([]);
        setLoading(false);
        return;
      }
    }
    setLocalTaskList(transformedTasks);
    taskListCache[projectId] = transformedTasks;
    cacheTimestamp[projectId] = Date.now();
    setLoading(false);
  }, [projectId, contextTaskList]);

  useEffect(() => {
    loadTasks();
  }, [projectId, contextTaskList]);

  useEffect(() => {
    if (projectId) preloadTasks(projectId);
  }, [projectId]);

  // Small tasks are handled by ListJobSmall component

  // Lắng nghe sự kiện cập nhật tasks để đồng bộ dữ liệu
  useEffect(() => {
    const handleTasksUpdated = () => {
      // Clear cache để đảm bảo load dữ liệu mới
      if (taskListCache[projectId]) {
        delete taskListCache[projectId];
        delete cacheTimestamp[projectId];
      }

      // Reload tasks nếu không có context taskList
      if (!contextTaskList && projectId) {
        loadTasks();
      }
    };

    window.addEventListener('projectTasksUpdated', handleTasksUpdated);
    return () => {
      window.removeEventListener('projectTasksUpdated', handleTasksUpdated);
    };
  }, [projectId, contextTaskList, loadTasks]);

  useEffect(() => {
    if (!projectId) return;
    setLoadingMembers(true);
    getProjectMembers(projectId)
      .then((res) => {
        const rawUsers = res.data || res || [];
        const users = rawUsers.map((m) => transformUserData(m.user || m));
        setProjectMembers(users);
        setLoadingMembers(false);
      })
      .catch(() => {
        setProjectMembers([]);
        setLoadingMembers(false);
      });
  }, [projectId]);

  // Disable page scroll for this component
  useEffect(() => {
    const dashboardMain = document.querySelector(".dashboard-main");
    if (dashboardMain) {
      dashboardMain.classList.add("no-page-scroll");
    }

    return () => {
      if (dashboardMain) {
        dashboardMain.classList.remove("no-page-scroll");
      }
    };
  }, []);

  // Đóng popup khi click ra ngoài
  useEffect(() => {
    const handleClickOutside = (event) => {
      // Đóng status popup
      if (
        showStatusPopup &&
        popupRef.current &&
        !popupRef.current.contains(event.target) &&
        statusCellRef.current &&
        !statusCellRef.current.contains(event.target)
      ) {
        setShowStatusPopup(false);
      }

      // Đóng priority popup
      if (
        showPriorityPopup &&
        priorityPopupRef.current &&
        !priorityPopupRef.current.contains(event.target) &&
        priorityCellRef.current &&
        !priorityCellRef.current.contains(event.target)
      ) {
        setShowPriorityPopup(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showStatusPopup, showPriorityPopup]);

  // Xóa hiệu ứng đổi trạng thái sau 0.5s
  useEffect(() => {
    if (changingTaskId) {
      const timer = setTimeout(() => {
        setChangingTaskId(null);
      }, 500); // Thời gian trùng với animation trong CSS

      return () => clearTimeout(timer);
    }
  }, [changingTaskId]);

  // Xử lý đóng action menu khi click ra ngoài
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (showActionMenu && !event.target.closest('.more-actions') && !event.target.closest('.action-menu')) {
        setShowActionMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showActionMenu]);

  // Hàm chuyển chuỗi ngày (DD/MM/YYYY) sang object Date
  const parseDate = (dateStr) => {
    if (!dateStr) return null;
    const [day, month, year] = dateStr.split("/").map(Number);
    return new Date(year, month - 1, day);
  };

  // Hàm sắp xếp danh sách công việc
  const sortTasks = (tasks, sortOption) => {
    if (!sortOption) return tasks;

    const sortedTasks = [...tasks];

    switch (sortOption) {
      case "name-asc":
        sortedTasks.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case "name-desc":
        sortedTasks.sort((a, b) => b.name.localeCompare(a.name));
        break;
      case "create-newest":
        sortedTasks.sort((a, b) => {
          const dateA = parseDate(a.startDate);
          const dateB = parseDate(b.startDate);
          if (!dateA || !dateB) return 0;
          return dateB - dateA;
        });
        break;
      case "create-oldest":
        sortedTasks.sort((a, b) => {
          const dateA = parseDate(a.startDate);
          const dateB = parseDate(b.startDate);
          if (!dateA || !dateB) return 0;
          return dateA - dateB;
        });
        break;
      case "end-newest":
        sortedTasks.sort((a, b) => {
          const dateA = parseDate(a.dueDate);
          const dateB = parseDate(b.dueDate);
          if (!dateA || !dateB) return 0;
          return dateB - dateA;
        });
        break;
      case "end-oldest":
        sortedTasks.sort((a, b) => {
          const dateA = parseDate(a.dueDate);
          const dateB = parseDate(b.dueDate);
          if (!dateA || !dateB) return 0;
          return dateA - dateB;
        });
        break;
      default:
        break;
    }

    // Sắp xếp cả các task con nếu có
    return sortedTasks.map((task) => {
      if (task.children && task.children.length > 0) {
        return {
          ...task,
          children: sortTasks(task.children, sortOption),
        };
      }
      return task;
    });
  };

  // Hàm lọc danh sách công việc theo filterOptions
  const filterTasks = (tasks, filterOptions) => {
    if (
      !filterOptions ||
      (!filterOptions.status?.length &&
        !filterOptions.dateRange?.startDate &&
        !filterOptions.dateRange?.endDate &&
        !filterOptions.dateRange?.dueStartDate &&
        !filterOptions.dateRange?.dueEndDate)
    ) {
      return tasks;
    }
    // Lọc từng task cha
    const filteredTasks = tasks
      .map((task) => {
        // Lọc các task con trước (nếu có)
        let filteredChildren = [];
        if (task.children && task.children.length > 0) {
          filteredChildren = filterTasks(task.children, filterOptions);
        }
        // Kiểm tra task này có thỏa mãn filter không
        let isMatch = true;
        // Lọc theo trạng thái
        if (
          filterOptions.status?.length > 0 &&
          !filterOptions.status.includes(task.status)
        ) {
          isMatch = false;
        }
        // Lọc theo ngày bắt đầu
        if (
          isMatch &&
          (filterOptions.dateRange?.startDate ||
            filterOptions.dateRange?.endDate)
        ) {
          const taskStartDate = parseDate(task.startDate);
          if (!taskStartDate) isMatch = true; // Nếu không có ngày thì bỏ qua
          if (filterOptions.dateRange.startDate) {
            const filterStartDate = new Date(filterOptions.dateRange.startDate);
            if (taskStartDate < filterStartDate) isMatch = false;
          }
          if (filterOptions.dateRange.endDate) {
            const filterEndDate = new Date(filterOptions.dateRange.endDate);
            if (taskStartDate > filterEndDate) isMatch = false;
          }
        }
        // Lọc theo ngày kết thúc
        if (
          isMatch &&
          (filterOptions.dateRange?.dueStartDate ||
            filterOptions.dateRange?.dueEndDate)
        ) {
          const taskDueDate = parseDate(task.dueDate);
          if (!taskDueDate) isMatch = true;
          if (filterOptions.dateRange.dueStartDate) {
            const filterDueStartDate = new Date(
              filterOptions.dateRange.dueStartDate
            );
            if (taskDueDate < filterDueStartDate) isMatch = false;
          }
          if (filterOptions.dateRange.dueEndDate) {
            const filterDueEndDate = new Date(
              filterOptions.dateRange.dueEndDate
            );
            if (taskDueDate > filterDueEndDate) isMatch = false;
          }
        }
        // Nếu task này hoặc task con thỏa mãn thì giữ lại
        if (isMatch || (filteredChildren && filteredChildren.length > 0)) {
          return {
            ...task,
            children: filteredChildren,
          };
        }
        // Không thỏa mãn thì loại bỏ
        return null;
      })
      .filter(Boolean);
    return filteredTasks;
  };

  // Kết hợp lọc và sắp xếp, dùng useMemo để tối ưu hiệu năng
  const processedTasks = useMemo(() => {
    let processed = [...taskList];
    
    processed = filterTasks(processed, filterOptions);
    processed = sortTasks(processed, sortOption);
    return processed;
  }, [taskList, sortOption, filterOptions]);

  // Lấy icon trạng thái theo status
  const getStatusIcon = (status) => {
    switch (status) {
      case "in_progress":
        return deploymentIcon;
      case "completed":
        return completeIcon;
      case "pending":
      case "waiting":
        return waitingIcon;
      case "overdue":
        return overdueIcon;
      case "review":
        return considerIcon;
      default:
        return waitingIcon;
    }
  };

  // Lấy text trạng thái
  const getStatusText = (status) => {
    switch (status) {
      case "in_progress":
        return "Đang triển khai";
      case "completed":
        return "Hoàn thành";
      case "pending":
      case "waiting":
        return "Đang chờ";
      case "overdue":
        return "Quá hạn";
      case "review":
        return "Xem xét";
      default:
        return status;
    }
  };

  // Lấy icon mức độ ưu tiên
  const getPriorityIcon = (priority) => {
    const p = String(priority).toLowerCase();
    switch (p) {
      case "high":
      case "cao":
        return highPriorityIcon; // đỏ
      case "medium":
      case "trung bình":
      case "trung binh":
        return mediumPriorityIcon; // cam
      case "low":
      case "thấp":
      case "thap":
        return normalPriorityIcon; // xanh lá
      default:
        return normalPriorityIcon;
    }
  };

  // Lấy text mức độ ưu tiên
  const getPriorityText = (priority) => {
    if (!priority) return "Chưa xác định";
    const p = String(priority).toLowerCase();
    switch (p) {
      case "high":
      case "cao":
        return "Cao";
      case "medium":
      case "trung bình":
      case "trung binh":
        return "Trung bình";
      case "low":
      case "thấp":
      case "thap":
        return "Thấp";
      default:
        if (["Cao", "Trung bình", "Thấp"].includes(priority)) return priority;
        return "Thấp";
    }
  };

  // Xử lý đổi trạng thái công việc
  const handleStatusChange = async (taskId, newStatus) => {
    setChangingTaskId(taskId);

    try {
      // Map frontend status to backend status
      const frontendToBackendStatusMap = {
        "waiting": "pending",
        "in_progress": "in_progress",
        "review": "review",
        "completed": "completed",
        "overdue": "overdue"
      };

      const backendStatus = frontendToBackendStatusMap[newStatus] || newStatus;
      await updateProjectTask(projectId, taskId, { status: backendStatus });

      // Cập nhật local state
      const updatedTasks = taskList.map((task) => {
        if (task.id === taskId) {
          return {
            ...task,
            status: newStatus,
            statusColor:
              newStatus === "completed"
                ? "#52c41a"
                : newStatus === "overdue"
                ? "#ff4d4f"
                : newStatus === "review"
                ? "#722ed1"
                : newStatus === "in_progress"
                ? "#1890ff"
                : "#8c8c8c",
          };
        }
        if (task.children) {
          return {
            ...task,
            children: task.children.map((child) =>
              child.id === taskId
                ? {
                    ...child,
                    status: newStatus,
                    statusColor:
                      newStatus === "completed"
                        ? "#52c41a"
                        : newStatus === "overdue"
                        ? "#ff4d4f"
                        : newStatus === "review"
                        ? "#722ed1"
                        : newStatus === "in_progress"
                        ? "#1890ff"
                        : "#8c8c8c",
                  }
                : child
            ),
          };
        }
        return task;
      });

      setTaskList(updatedTasks);

      if (selectedTask && selectedTask.id === taskId) {
        setSelectedTask({ ...selectedTask, status: newStatus });
      }

      const statusText = getStatusText(newStatus);

      // Phát sự kiện đồng bộ
      window.dispatchEvent(new Event("projectTasksUpdated"));
    } catch (err) {
      console.error("Error updating task status:", err);
      // Có thể hiển thị thông báo lỗi cho user
    }

    setShowStatusPopup(false);
  };

  // Xử lý đổi mức độ ưu tiên công việc
  const handlePriorityChange = useCallback(async (taskId, newPriority) => {
    try {
      setChangingTaskId(taskId);
      
      // Clear cache để đảm bảo load dữ liệu mới
      if (taskListCache[projectId]) {
        delete taskListCache[projectId];
        delete cacheTimestamp[projectId];
      }
      
      const response = await updateProjectTask(projectId, taskId, {
        priority: newPriority,
      });

      if (response.success) {
        // Cập nhật local state
        setTaskList((prevTasks) =>
          prevTasks.map((task) =>
            task.id === taskId ? { ...task, priority: newPriority } : task
          )
        );
        
        // Refresh data từ server
        await loadTasks();
        
        showSuccess("Cập nhật mức độ ưu tiên thành công!");
      } else {
        showError("Có lỗi xảy ra khi cập nhật mức độ ưu tiên");
      }
    } catch (error) {
      console.error("Lỗi cập nhật priority:", error);
      showError("Có lỗi xảy ra khi cập nhật mức độ ưu tiên");
    } finally {
      setChangingTaskId(null);
      setShowPriorityPopup(false);
    }
  }, [projectId, loadTasks]);

  // Xử lý hover vào trạng thái để hiện popup
  const handleStatusHover = (e, task) => {
    statusCellRef.current = e.currentTarget;
    const rect = e.currentTarget.getBoundingClientRect();

    // Calculate the position to prevent layout shifting
    const position = {
      top: rect.bottom,
      left: rect.left,
    };

    setPopupPosition(position);

    setActiveTaskId(task.id);
    setHoveredStatus(task.status);
    setShowStatusPopup(true);
  };

  // Xử lý mouse leave khỏi trạng thái
  const handleMouseLeave = (e) => {
    // Nếu di chuột sang popup thì không đóng
    const toElement = e.relatedTarget;
    if (popupRef.current && popupRef.current.contains(toElement)) {
      return;
    }

    // Delay nhỏ để cho phép di chuyển chuột sang popup
    setTimeout(() => {
      if (
        popupRef.current &&
        !popupRef.current.matches(":hover") &&
        statusCellRef.current &&
        !statusCellRef.current.matches(":hover")
      ) {
        setShowStatusPopup(false);
      }
    }, 100);
  };

  // Xử lý hover vào mức độ ưu tiên để hiện popup
  const handlePriorityHover = (e, task) => {
    priorityCellRef.current = e.currentTarget;
    const rect = e.currentTarget.getBoundingClientRect();

    // Calculate the position to prevent layout shifting
    setPriorityPopupPosition({
      top: rect.bottom,
      left: rect.left,
    });

    setActiveTaskId(task.id);
    setHoveredPriority(task.priority);
    setShowPriorityPopup(true);
  };

  // Xử lý mouse leave khỏi mức độ ưu tiên
  const handlePriorityMouseLeave = (e) => {
    // Nếu di chuột sang popup thì không đóng
    const toElement = e.relatedTarget;
    if (
      priorityPopupRef.current &&
      priorityPopupRef.current.contains(toElement)
    ) {
      return;
    }

    // Delay nhỏ để cho phép di chuyển chuột sang popup
    setTimeout(() => {
      if (
        priorityPopupRef.current &&
        !priorityPopupRef.current.matches(":hover") &&
        priorityCellRef.current &&
        !priorityCellRef.current.matches(":hover")
      ) {
        setShowPriorityPopup(false);
      }
    }, 100);
  };

  // Xử lý hover vào avatar thành viên để hiện tooltip
  const handleAssigneeHover = (e, member) => {
    const rect = e.currentTarget.getBoundingClientRect();
    setTooltipPosition({
      top: rect.top - 35 + window.scrollY, // Hiện tooltip phía trên avatar
      left: rect.left + rect.width / 2 + window.scrollX, // Vị trí trung tâm avatar (CSS sẽ căn giữa với translateX(-50%))
    });
    setTooltipContent(member.name);
    setShowAssigneeTooltip(true);
  };

  // Xử lý mouse leave khỏi avatar thành viên
  const handleAssigneeLeave = () => {
    setShowAssigneeTooltip(false);
  };

  // Removed hardcoded small tasks handlers

  // Xử lý cập nhật API child task
  const handleApiChildTaskUpdate = async (childTaskId, parentTaskId, newStatus) => {
    try {
      // Cập nhật trạng thái task con qua API
      await updateProjectTask(projectId, childTaskId, { status: newStatus });
      
      // Cập nhật local state
      const updatedTasks = taskList.map((task) => {
        if (task.id === parentTaskId && task.children) {
          return {
            ...task,
            children: task.children.map((child) =>
              child.id === childTaskId ? { ...child, status: newStatus } : child
            ),
          };
        }
        return task;
      });
      
      setTaskList(updatedTasks);
      showSuccess("Cập nhật trạng thái công việc phụ thành công!");
      
      // Phát sự kiện đồng bộ
      window.dispatchEvent(new Event("projectTasksUpdated"));
    } catch (error) {
      console.error("Error updating API child task:", error);
      showError("Có lỗi xảy ra khi cập nhật công việc phụ");
    }
  };

  // Xử lý xóa API child task
  const handleApiChildTaskDelete = async (childTaskId, parentTaskId) => {
    try {
      if (window.confirm('Bạn có chắc chắn muốn xóa công việc phụ này?')) {
        await deleteProjectTask(projectId, childTaskId);
        
        // Cập nhật local state
        const updatedTasks = taskList.map((task) => {
          if (task.id === parentTaskId && task.children) {
            return {
              ...task,
              children: task.children.filter((child) => child.id !== childTaskId),
            };
          }
          return task;
        });
        
        setTaskList(updatedTasks);
        showSuccess("Xóa công việc phụ thành công!");
        
        // Phát sự kiện đồng bộ
        window.dispatchEvent(new Event("projectTasksUpdated"));
      }
    } catch (error) {
      console.error("Error deleting API child task:", error);
      showError("Có lỗi xảy ra khi xóa công việc phụ");
    }
  };

  // ---------------------------------------- Render popup trạng thái
  const StatusPopup = () => {
    if (!showStatusPopup) return null;

    const statuses = [
      { value: "waiting", label: "Đang chờ", icon: waitingIcon },
      { value: "in_progress", label: "Đang triển khai", icon: deploymentIcon },
      { value: "completed", label: "Hoàn thành", icon: completeIcon },
      { value: "overdue", label: "Quá hạn", icon: overdueIcon },
      { value: "review", label: "Xem xét", icon: considerIcon },
    ];

    return (
      <div
        className="status-popup"
        style={{
          top: `${popupPosition.top}px`,
          left: `${popupPosition.left}px`,
          pointerEvents: "auto",
        }}
        ref={popupRef}
        onMouseLeave={handleMouseLeave}
      >
        <div className="status-popup-header">Trạng thái</div>
        <div className="status-popup-options">
          {statuses.map((status) => (
            <div
              key={status.value}
              className={`status-option ${
                hoveredStatus === status.value ? "active" : ""
              }`}
              onClick={() => handleStatusChange(activeTaskId, status.value)}
            >
              <img src={status.icon} alt="" className="status-icon" />
              <span>{status.label}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // ---------------------------------------- Render popup mức độ ưu tiên
  const PriorityPopup = () => {
    if (!showPriorityPopup) return null;

    const priorities = [
      { value: "low", label: "Thấp", icon: normalPriorityIcon },
      { value: "medium", label: "Trung bình", icon: mediumPriorityIcon },
      { value: "high", label: "Cao", icon: highPriorityIcon },
    ];

    return (
      <div
        className="priority-popup"
        style={{
          top: `${priorityPopupPosition.top}px`,
          left: `${priorityPopupPosition.left}px`,
          pointerEvents: "auto",
        }}
        ref={priorityPopupRef}
        onMouseLeave={handlePriorityMouseLeave}
      >
        <div className="priority-popup-header">Mức độ</div>
        <div className="priority-popup-options">
          {priorities.map((priority) => (
            <div
              key={priority.value}
              className={`priority-option ${
                hoveredPriority === priority.value ? "active" : ""
              }`}
              onClick={() => handlePriorityChange(activeTaskId, priority.value)}
            >
              <img src={priority.icon} alt="" className="priority-icon" />
              <span>{priority.label}</span>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Render assignee tooltip
  const AssigneeTooltip = () => {
    if (!showAssigneeTooltip) return null;

    return (
      <div
        className="assignee-tooltip"
        style={{
          top: `${tooltipPosition.top}px`,
          left: `${tooltipPosition.left}px`,
          pointerEvents: "auto",
        }}
        ref={tooltipRef}
      >
        {tooltipContent}
      </div>
    );
  };

  // Kiểm tra task có được chọn không
  const isTaskChecked = (taskId) => selectedTaskIds.includes(taskId);

  // Lấy tất cả task id (bao gồm cả con)
  const getAllTaskIds = (tasks) => {
    let ids = [];
    tasks.forEach((task) => {
      ids.push(task.id);
      if (task.children && task.children.length > 0) {
        ids = ids.concat(getAllTaskIds(task.children));
      }
    });
    return ids;
  };

  // Xử lý chọn/bỏ chọn 1 task
  const handleTaskCheckboxChange = (taskId) => {
    setSelectedTaskIds((prev) => {
      const newSelected = prev.includes(taskId)
        ? prev.filter((id) => id !== taskId)
        : [...prev, taskId];
      return newSelected;
    });
  };

  // Xử lý chọn/bỏ chọn tất cả
  const handleSelectAllChange = () => {
    const allIds = getAllTaskIds(processedTasks);
    if (allIds.every((id) => selectedTaskIds.includes(id))) {
      setSelectedTaskIds([]);
    } else {
      setSelectedTaskIds(allIds);
    }
  };

  // Xử lý huỷ chọn tất cả
  const handleCancelSelect = () => {
    setSelectedTaskIds([]);
  };

  // Xử lý xoá các task được chọn
  const handleDeleteSelected = async () => {
    try {
      // Xóa từng task được chọn
      for (const taskId of selectedTaskIds) {
        await deleteProjectTask(projectId, taskId);
      }

      // Cập nhật local state
      const updatedTasks = taskList.filter(
        (task) => !selectedTaskIds.includes(task.id)
      );
      setTaskList(updatedTasks);
      setSelectedTaskIds([]);

      showSuccess("Xóa công việc thành công!");
      
      // Phát sự kiện đồng bộ
      window.dispatchEvent(new Event("projectTasksUpdated"));
    } catch (err) {
      console.error("Error deleting tasks:", err);
      showError("Có lỗi xảy ra khi xóa công việc: " + (err.message || err));
    }
  };

  // Xử lý tạo nhiệm vụ phụ
  const handleCreateSubTask = (parentTask) => {
    // Hiển thị form tạo nhiệm vụ phụ
    setParentTaskForSubTask(parentTask);
    setShowSubTaskForm(true);
    setShowActionMenu(false);
  };

  // Xử lý khi tạo nhiệm vụ phụ từ form
  const handleSubTaskSubmit = async (jobData) => {
    try {
      console.log('Creating sub-task with data:', jobData);
      console.log('Parent task:', parentTaskForSubTask);
      
      // Gọi API tạo task thực sự trên backend
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:3000/api/projects/${projectId}/tasks`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(jobData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Có lỗi xảy ra khi tạo công việc');
      }

      const result = await response.json();
      console.log('Sub-task created successfully:', result);

      if (result.success) {
        // Tạo sub-task object để hiển thị trong UI
        const newSubTask = {
          id: result.data._id || result.data.id,
          taskCode: result.data.taskCode, // Thêm taskCode từ backend
          name: jobData.title,
          status: "waiting",
          priority: jobData.priority,
          assignee: jobData.membersData || [],
          dueDate: jobData.dueDate,
          startDate: jobData.startDate,
          creator: jobData.createdById,
          description: jobData.description,
          progress: 0,
          activities: [],
          attachments: jobData.attachments || [],
          parentId: parentTaskForSubTask.id,
          projectId: projectId,
          estimatedDays: jobData.estimatedDays,
          dependentTask: jobData.dependentTask,
          // Thêm dependencies từ response backend
          dependencies: result.data.dependentTaskId ? 
            (typeof result.data.dependentTaskId === 'object' ? 
              [`${result.data.dependentTaskId.taskCode || 'TASK'} ${result.data.dependentTaskId.title || 'Chưa có tên'}`] :
              [result.data.dependentTaskId]
            ) : [],
          isRecurring: jobData.isRecurring,
          recurringFrequency: jobData.recurringFrequency,
          weeklyDays: jobData.weeklyDays,
        };

        // Thêm task con vào đầu danh sách và đảm bảo parent task được expand
        const updatedTasks = taskList.map(task => {
          if (task.id === parentTaskForSubTask.id) {
            return {
              ...task,
              children: [newSubTask, ...(task.children || [])],
              showChildren: true // Đảm bảo hiển thị sub-tasks
            };
          }
          return task;
        });

        // Cập nhật UI ngay lập tức trước khi reload
        const updatedTasksImmediate = taskList.map(task => {
          if (task.id === parentTaskForSubTask.id) {
            return {
              ...task,
              children: [newSubTask, ...(task.children || [])],
              showChildren: true // Auto expand để hiển thị sub-task mới
            };
          }
          return task;
        });
        setTaskList(updatedTasksImmediate);
        
        setShowSubTaskForm(false);
        setParentTaskForSubTask(null);
        
        showSuccess("Tạo nhiệm vụ phụ thành công!");
        
        // Reload lại danh sách tasks để đảm bảo đồng bộ với backend (background)
        setTimeout(() => {
          loadTasks();
        }, 1000);
      } else {
        throw new Error(result.message || 'Có lỗi xảy ra khi tạo công việc');
      }
    } catch (error) {
      console.error('Error creating sub-task:', error);
      showError(error.message || 'Có lỗi xảy ra khi tạo công việc phụ');
    }
  };

  // Xử lý khi hủy tạo nhiệm vụ phụ
  const handleSubTaskCancel = () => {
    setShowSubTaskForm(false);
    setParentTaskForSubTask(null);
  };

  // Hàm chuẩn bị task con cho hiển thị trong JobDetail
  const prepareChildTaskForDetail = (childTask, parentTask) => {
    // Tạo bản sao của task con
    const enhancedChildTask = { ...childTask };

    // Thêm các thuộc tính cần thiết từ task cha nếu task con không có
    if (!enhancedChildTask.startDate)
      enhancedChildTask.startDate = parentTask.startDate;
    if (!enhancedChildTask.description)
      enhancedChildTask.description = `Công việc con của: ${parentTask.name}`;
    if (!enhancedChildTask.creator)
      enhancedChildTask.creator = parentTask.creator;
    if (!enhancedChildTask.activities) enhancedChildTask.activities = [];
    if (!enhancedChildTask.attachments) enhancedChildTask.attachments = [];
    if (!enhancedChildTask.progress)
      enhancedChildTask.progress = childTask.status === "completed" ? 100 : 0;
    // Thêm code nếu không có
    if (!enhancedChildTask.code) enhancedChildTask.code = childTask.id;
    // ĐẢM BẢO projectId luôn có
    enhancedChildTask.projectId = projectId;

    return enhancedChildTask;
  };

  // Xử lý khi click vào tên task
  const handleTaskClick = (task, isChild = false, parentTask = null) => {
    if (isChild && parentTask) {
      // Nếu là task con, chuẩn bị thêm thông tin cần thiết
      const enhancedTask = prepareChildTaskForDetail(task, parentTask);
      setSelectedTask(enhancedTask);
    } else {
      // Nếu là task cha, hiển thị như bình thường
      setSelectedTask({ ...task, projectId });
    }
  };

  // Render loading state
  if (loading) {
    return (
      <div className="lists-container">
        <div
          className="lists-content-wrapper"
          style={{
            position: "relative",
            height: "100%",
            minHeight: "0",
            width: "100%",
          }}
        >
          <div
            className="task-view-wrapper"
            style={{
              width: "100%",
              height: "100%",
              minHeight: "0",
              overflow: "hidden",
              position: "relative",
            }}
          >
            <div
              className="task-view"
              style={{
                height: "100%",
                minHeight: 0,
                maxHeight: "100%",
                width: "100%",
              }}
            >
              <div className="tasks-table-container">
                <table className="task-table" style={{ width: "100%" }}>
                  <thead>
                    <tr>
                      <th className="task-name-col">Tên công việc</th>
                      <th className="task-status-col">Trạng thái</th>
                      <th className="task-priority-col">Mức độ</th>
                      <th className="task-assignee-col">Thành viên thực hiện</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Array.from({ length: 8 }).map((_, idx) => (
                      <tr key={idx} style={{ opacity: 0.7 }}>
                        <td>
                          <div
                            style={{
                              display: "flex",
                              alignItems: "center",
                              gap: 12,
                            }}
                          >
                            <div
                              style={{
                                width: 120,
                                height: 16,
                                background: "#f0f0f0",
                                borderRadius: 4,
                                marginBottom: 6,
                                animation: "pulse 1.5s ease-in-out infinite",
                              }}
                            ></div>
                          </div>
                        </td>
                        <td>
                          <div
                            style={{
                              width: 80,
                              height: 14,
                              background: "#f0f0f0",
                              borderRadius: 4,
                              animation: "pulse 1.5s ease-in-out infinite",
                            }}
                          ></div>
                        </td>
                        <td>
                          <div
                            style={{
                              width: 60,
                              height: 14,
                              background: "#f0f0f0",
                              borderRadius: 4,
                              animation: "pulse 1.5s ease-in-out infinite",
                            }}
                          ></div>
                        </td>
                        <td>
                          <div
                            style={{
                              width: 40,
                              height: 14,
                              background: "#f0f0f0",
                              borderRadius: 4,
                              animation: "pulse 1.5s ease-in-out infinite",
                            }}
                          ></div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <style>{`
                @keyframes pulse {
                  0% { opacity: 1; }
                  50% { opacity: 0.5; }
                  100% { opacity: 1; }
                }
              `}</style>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className="lists-container">
        <div
          className="lists-content-wrapper"
          style={{
            position: "relative",
            height: "100%",
            minHeight: "0",
            width: "100%",
          }}
        >
          <div
            className="task-view-wrapper"
            style={{
              width: "100%",
              height: "100%",
              minHeight: "0",
              overflow: "hidden",
              position: "relative",
            }}
          >
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                height: "80vh",
                color: "#ff6b6b",
              }}
            >
              <div>Lỗi: {error}</div>
              <button
                onClick={() => window.location.reload()}
                style={{
                  marginTop: 16,
                  padding: "8px 16px",
                  backgroundColor: "#007bff",
                  color: "white",
                  border: "none",
                  borderRadius: 4,
                  cursor: "pointer",
                }}
              >
                Thử lại
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render một dòng công việc (cả cha và con)
  const renderTaskRow = (task, isChild = false, parentTask = null) => (
    <React.Fragment key={task.id + task.name}>
      <tr
        style={{ cursor: "pointer" }}
        onClick={(e) => {
          // Ngăn không cho click vào checkbox, status, priority hoặc assignee trigger toggle
          if (
            e.target.type === "checkbox" ||
            e.target.closest(".task-status") ||
            e.target.closest(".task-priority") ||
            e.target.closest(".assignee-avatar") ||
            e.target.closest(".more-actions")
          ) {
            return;
          }
          
          // Toggle children cho task cha
          if (!isChild && task.children && task.children.length > 0) {
            setTaskList(prev => 
              prev.map(t => 
                t.id === task.id ? {...t, showChildren: !t.showChildren} : t
              )
            );
          }
          // Removed hardcoded TSK-01 logic
        }}
      >
        <td>
          <div
            className="task-id-name"
            style={
              isChild
                ? { paddingLeft: 26 }
                : (!isChild && (!task.children || task.children.length === 0))
                ? { paddingLeft: 26 }
                : {}
            }
          >
            {/* Toggle arrow for tasks with children */}
            {!isChild && task.children && task.children.length > 0 && (
              <img
                src={task.showChildren ? arrowbottomIcon : arrowrightIcon}
                alt="toggle"
                style={{ 
                  width: "16px", 
                  height: "16px", 
                  marginRight: "8px",
                  cursor: "pointer",
                  transition: "transform 0.2s"
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  setTaskList(prev => 
                    prev.map(t => 
                      t.id === task.id ? {...t, showChildren: !t.showChildren} : t
                    )
                  );
                }}
              />
            )}
            <span className="task-name">
              {task.name}
            </span>
            {task.activities && task.activities.length > 0 && (
              <span className="task-comments">
                {/* <img src={cmtIcon} alt="comments" className="cmt-icon" />
                <span className="comments-count">{task.activities.length}</span> */}
              </span>
            )}
            {/* Đã xoá nút xoá mềm (move to trash) theo yêu cầu */}
          </div>
        </td>
        <td className="task-status-cell">
          <div
            className={`task-status ${
              // Map status to CSS class
              task.status === "pending" || task.status === "waiting"
                ? "pending"
                : task.status === "in_progress"
                ? "in-progress"
                : task.status === "completed"
                ? "completed"
                : task.status === "overdue"
                ? "overdue"
                : task.status === "review"
                ? "review"
                : "pending"
            } ${changingTaskId === task.id ? "status-changing" : ""}`}
            onMouseEnter={(e) => handleStatusHover(e, task)}
            onMouseLeave={handleMouseLeave}
          >
            <img
              src={getStatusIcon(task.status)}
              alt=""
              className="status-icon"
            />
            <span className="status-text">{getStatusText(task.status)}</span>
          </div>
        </td>
        <td className="task-priority-cell">
          <div
            className={`task-priority ${task.priority}`}
            onMouseEnter={(e) => handlePriorityHover(e, task)}
            onMouseLeave={handlePriorityMouseLeave}
          >
            <img
              src={getPriorityIcon(task.priority)}
              alt=""
              className="priority-icon"
            />
            <span className="priority-text">
              {getPriorityText(task.priority)}
            </span>
          </div>
        </td>
        <td className="task-assignee-cell">
          <div className="assignee" style={{ display: "flex", alignItems: "center" }}>
            {task.assignee && task.assignee.length > 0 ? (
              task.assignee.map((member, index) => {
                // Fallback: nếu không có avatar hoặc rỗng thì dùng randomuser.me
                let avatarUrl = member.avatar && member.avatar.trim() !== "" ? member.avatar : "https://randomuser.me/api/portraits/men/1.jpg";
                return (
                  <img
                    key={member.userId || index}
                    src={avatarUrl}
                    alt={member.name || "avatar"}
                    className="assignee-avatar"
                    style={{
                      width: "32px",
                      height: "32px",
                      borderRadius: "50%",
                      objectFit: "cover",
                      marginRight: index < task.assignee.length - 1 ? "4px" : "0",
                      background: "#f0f0f0",
                      border: "1px solid #e0e0e0",
                      cursor: "pointer"
                    }}
                    onMouseEnter={(e) => handleAssigneeHover(e, member)}
                    onMouseLeave={handleAssigneeLeave}
                    onError={e => { e.currentTarget.onerror = null; e.currentTarget.src = defaultAvatar; }}
                  />
                );
              })
            ) : (
              <img
                src={defaultAvatar}
                alt="avatar"
                className="assignee-avatar"
                style={{
                  width: "32px",
                  height: "32px",
                  borderRadius: "50%",
                  objectFit: "cover",
                  opacity: 0.5,
                  background: "#f0f0f0",
                  border: "1px solid #e0e0e0"
                }}
              />
            )}
          </div>
        </td>
        <td className="task-progress-cell" style={{ width: "120px", verticalAlign: "middle" }}>
          <div style={{ display: "flex", flexDirection: "column", alignItems: "center", justifyContent: "center" }}>
            <div style={{ width: "80px", textAlign: "right", fontSize: "13px", color: "#333", marginBottom: "2px" }}>{`${task.progress || 0}%`}</div>
            <div className="progress-bar" style={{ width: "80px", height: "6px", backgroundColor: "#f0f0f0", borderRadius: "3px", overflow: "hidden" }}>
              <div 
                className="progress-fill"
                style={{
                  width: `${task.progress || 0}%`,
                  height: "100%",
                  backgroundColor: task.progress === 100 ? '#52c41a' : '#1890ff'
                }}
              />
            </div>
          </div>
        </td>
        <td className="task-actions-cell" style={{ width: "50px", textAlign: "center" }}>
          <div 
            className="more-actions" 
            style={{ cursor: "pointer", fontSize: "16px", color: "#595959", position: "relative", fontWeight: "bold" }}
            onClick={(e) => {
              e.stopPropagation();
              const rect = e.currentTarget.getBoundingClientRect();
              setActionMenuPosition({ 
                top: rect.bottom + window.scrollY,
                left: rect.left + window.scrollX - 100 // Dịch sang trái 100px
              });
              setSelectedActionTask(task);
              setShowActionMenu(true);
            }}
          >
            ...
          </div>
        </td>
      </tr>

      {/* Render child tasks dropdown - only when toggled */}
      {!isChild && task.children && task.children.length > 0 && task.showChildren && (
        <tr>
          <td colSpan="6" style={{ padding: 0, border: 'none' }}>
            <div className="small-tasks-dropdown">
              <div className="small-tasks-grid">
                {task.children.map((childTask) => (
                  <ListJobSmall
                    key={childTask.id}
                    task={{
                      id: childTask.id,
                      category: (() => {
                        // Ưu tiên taskCode từ backend
                        if (childTask.taskCode) return childTask.taskCode;
                        
                        // Nếu name có format "TASK-XXXXXX title" hoặc "SUB-XXXXXX title", extract taskCode
                        if (childTask.name) {
                          const match = childTask.name.match(/^(TASK-\d{6}|SUB-\d{6})/);
                          if (match) return match[1];
                        }
                        
                        // Fallback tạo SUB code
                        return `SUB-${String(task.children.indexOf(childTask) + 1).padStart(6, '0')}`;
                      })(),
                      title: (() => {
                        // Extract title từ name nếu có format "TASK-XXXXXX title"
                        if (childTask.name) {
                          const match = childTask.name.match(/^(?:TASK-\d{6}|SUB-\d{6})\s+(.+)$/);
                          if (match) return match[1];
                          return childTask.name;
                        }
                        return childTask.title || `Công việc con của: ${task.name}`;
                      })(),
                      description: childTask.description || `Công việc con của: ${task.name}`,
                      status: getStatusText(childTask.status),
                      priority: childTask.priority || 'medium',
                      startDate: childTask.startDate ? (() => {
                        const date = new Date(childTask.startDate);
                        const day = date.getDate().toString().padStart(2, '0');
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const year = date.getFullYear();
                        return `${day}/${month}/${year}`;
                      })() : null,
                      dueDate: childTask.dueDate ? (() => {
                        const date = new Date(childTask.dueDate);
                        const day = date.getDate().toString().padStart(2, '0');
                        const month = (date.getMonth() + 1).toString().padStart(2, '0');
                        const year = date.getFullYear();
                        return `${day}/${month}/${year}`;
                      })() : null,
                      estimatedTime: childTask.estimatedDays ? `${childTask.estimatedDays} ngày` : 'Không có',
                      assignee: {
                        name: childTask.assignee && childTask.assignee.length > 0 
                          ? childTask.assignee[0].name 
                          : childTask.assignedTo && childTask.assignedTo.fullName
                          ? childTask.assignedTo.fullName
                          : 'Chưa phân công'
                      },
                      attachments: childTask.attachments || [],
                      dependencies: childTask.dependentTaskId ? 
                        (typeof childTask.dependentTaskId === 'object' ? 
                          [`${childTask.dependentTaskId.taskCode || 'TASK'} ${childTask.dependentTaskId.title || 'Chưa có tên'}`] :
                          [childTask.dependentTaskId]
                        ) : [],
                      dependency: childTask.dependentTaskId ? 
                        (typeof childTask.dependentTaskId === 'object' ? 
                          `${childTask.dependentTaskId.taskCode || 'TASK'} ${childTask.dependentTaskId.title || 'Chưa có tên'}` :
                          childTask.dependentTaskId
                        ) : '',
                      // Thêm các trường từ backend để không bị mất khi reload
                      createdAt: childTask.createdAt,
                      updatedAt: childTask.updatedAt,
                      createdById: childTask.createdById,
                      projectId: childTask.projectId,
                      parentTaskId: childTask.parentTaskId
                    }}
                    onTaskUpdate={(updatedTasks) => {
                      // Handle update for API child tasks
                      handleApiChildTaskUpdate(childTask.id, task.id, updatedTasks);
                    }}
                    onTaskDelete={(updatedTasks) => {
                      // Handle delete for API child tasks
                      handleApiChildTaskDelete(childTask.id, task.id);
                    }}
                  />
                ))}
              </div>
            </div>
          </td>
        </tr>
      )}
    </React.Fragment>
  );

  return (
    <div className="lists-container">
      <div
        className="lists-content-wrapper"
        style={{
          position: "relative",
          height: "100%",
          minHeight: "0",
          width: "100%",
        }}
      >
        <div
          className="task-view-wrapper"
          style={{
            width: "100%",
            height: "100%",
            minHeight: "0",
            overflow: "hidden",
            position: "relative",
          }}
        >
          <div
            className="task-view"
            style={{
              height: "100%",
              minHeight: 0,
              maxHeight: "100%",
              paddingBottom: selectedTaskIds.length > 0 ? "60px" : "0",
              width: "100%",
            }}
          >
            {processedTasks.length === 0 ? (
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "80vh",
                  color: "#ccc",
                }}
              >
                <img
                  src={NoJobIcon}
                  alt="Công việc trống"
                  style={{ width: 600, marginBottom: 16 }}
                />
              </div>
            ) : (
              <div className="tasks-table-container">
                <table className="task-table" style={{ width: "100%" }}>
                  <thead>
                    <tr>
                      <th className="task-name-col">
                        Tên công việc
                      </th>
                      <th className="task-status-col">Trạng thái</th>
                      <th className="task-priority-col">Mức độ</th>
                      <th className="task-assignee-col">Thành viên thực hiện</th>
                      <th className="task-progress-col" style={{ width: "120px" }}>Tiến độ</th>
                      <th className="task-actions-col" style={{ width: "100px", textAlign: "center" }}>Hành động</th>
                    </tr>
                  </thead>
                  <tbody>
                    {processedTasks.map((parentTask) => (
                      <React.Fragment key={parentTask.id}>
                        {renderTaskRow(parentTask)}
                        {/* Child tasks are now rendered in dropdown, not as table rows */}
                      </React.Fragment>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          {/* Thanh action nổi khi có task được chọn */}
          {selectedTaskIds.length > 0 && (
            <div className="task-action-bar">
              <div style={{ marginRight: '16px', color: '#666' }}>
                Đã chọn: {selectedTaskIds.length} công việc
              </div>
              <button className="task-cancel-btn" onClick={handleCancelSelect}>
                Huỷ
              </button>
              <button className="delete-btn" onClick={handleDeleteSelected}>
                Xoá
              </button>
            </div>
          )}
        </div>

        {/* Chi tiết công việc hiển thị như trong MyJob.jsx */}
        {selectedTask && (
          <div
            style={{
              position: "absolute",
              top: 0,
              right: 0,
              width: "400px",
              height: "100%",
              zIndex: 50, // Giảm z-index để popup có thể hiển thị trên
            }}
          >
            <DetailJob
              task={selectedTask}
              onClose={() => setSelectedTask(null)}
              onTaskUpdate={(updatedTask) => {
                // Update the selected task immediately
                setSelectedTask(updatedTask);

                // Update the task in the task list
                setTaskList(prevTasks =>
                  prevTasks.map(task =>
                    task.id === updatedTask.id ? updatedTask : task
                  )
                );
              }}
              hideExport={true}
            />
          </div>
        )}
      </div>

      {/* Form tạo nhiệm vụ phụ */}
      {showSubTaskForm && parentTaskForSubTask && (
        <div
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            width: "100vw",
            height: "100vh",
            zIndex: 2000,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          <JobCreate
            onCancel={handleSubTaskCancel}
            onSubmit={handleSubTaskSubmit}
            projectId={projectId}
            projectMembers={projectMembers} // Truyền danh sách thành viên dự án thực tế
            loadingProjectMembers={loadingMembers}
            allUsers={projectMembers} // Sử dụng projectMembers làm allUsers cho sub-task
            loadingAllUsers={loadingMembers}
            isSubTaskCreation={true}
            parentTask={parentTaskForSubTask} // Truyền thông tin công việc chính
          />
        </div>
      )}

      {/* Container for UI elements like popups and tooltips */}
      <div
        className="ui-elements-container"
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          width: "100vw",
          height: "100vh",
          pointerEvents: "none",
          zIndex: 1000,
        }}
      >
        {/* Popup đổi trạng thái công việc */}
        <StatusPopup />
        {/* Popup đổi mức độ ưu tiên công việc */}
        <PriorityPopup />
        {/* Tooltip hiển thị tên thành viên khi hover avatar */}
        <AssigneeTooltip />
        {/* Menu hành động */}
        {showActionMenu && selectedActionTask && (
          <div 
            className="action-menu"
            style={{
              position: "fixed",
              top: `${actionMenuPosition.top}px`,
              left: `${actionMenuPosition.left}px`,
              backgroundColor: "white",
              boxShadow: "0 2px 8px rgba(0,0,0,0.15)",
              borderRadius: "4px",
              padding: "4px 0",
              zIndex: 1000,
              width: "160px",
              pointerEvents: "auto"
            }}
          >
            <div 
              className="menu-item"
              style={{
                padding: "8px 12px",
                cursor: "pointer",
                fontSize: "14px",
                color: "rgba(124, 124, 124, 1)",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                transition: "background 0.3s"
              }}
              onClick={(e) => {
                e.stopPropagation();
                handleTaskClick(selectedActionTask);
                setShowActionMenu(false);
              }}
              onMouseEnter={e => e.currentTarget.style.backgroundColor = "#f5f5f5"}
              onMouseLeave={e => e.currentTarget.style.backgroundColor = "transparent"}
            >
                               <img src={detailIcon} alt="" style={{ width: "16px", height: "16px", filter: "brightness(0) saturate(100%) invert(38%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0.9) contrast(1)" }} />
              <span>Xem chi tiết</span>
            </div>
            <div 
              className="menu-item"
              style={{
                padding: "8px 12px",
                cursor: "pointer",
                fontSize: "14px",
                color: "rgba(124, 124, 124, 1)",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                transition: "background 0.3s"
              }}
              onClick={(e) => {
                e.stopPropagation();
                // Xử lý tạo nhiệm vụ phụ
                handleCreateSubTask(selectedActionTask);
                setShowActionMenu(false);
              }}
              onMouseEnter={e => e.currentTarget.style.backgroundColor = "#f5f5f5"}
              onMouseLeave={e => e.currentTarget.style.backgroundColor = "transparent"}
            >
              <img src={addIcon} alt="" style={{ width: "16px", height: "16px",    filter: "brightness(0) saturate(100%) invert(38%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(0.9) contrast(1)" }} />
              <span>Tạo cv phụ</span>
            </div>
            <div 
              className="menu-item"
              style={{
                padding: "8px 12px",
                cursor: "pointer",
                fontSize: "14px",
                color: "#ff4d4f",
                display: "flex",
                alignItems: "center",
                gap: "8px",
                transition: "background 0.3s"
              }}
              onClick={(e) => {
                e.stopPropagation();
                // Xử lý xóa công việc
                handleDeleteSelected([selectedActionTask.id]);
                setShowActionMenu(false);
              }}
              onMouseEnter={e => e.currentTarget.style.backgroundColor = "#f5f5f5"}
              onMouseLeave={e => e.currentTarget.style.backgroundColor = "transparent"}
            >
                              <img src={trashIcon} alt="" style={{ width: "16px", height: "16px" }} />
                <span>Xóa công việc</span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ListProject; // Export component để sử dụng ở nơi khác