.task-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin: 0;
  max-width: 100%;
  font-family: 'Be Vietnam Pro', Arial, sans-serif;
  border: 1px solid #e5e7eb;
  transition: box-shadow 0.2s ease;
}

.task-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Header Section */
.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.task-title-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.task-category {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
}

.task-status {
  font-size: 13px;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.status-badge.status-waiting-dependency {
  background: #fef2f2;
  color: #dc2626;
  border-color: #fecaca;
}

.status-icon {
  width: 14px;
  height: 14px;
}

.task-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.start-button {
  background: #3b82f6;
  color: white;
}

.start-button:hover {
  background: #2563eb;
}

.complete-button {
  background: #10b981;
  color: white;
}

.complete-button:hover {
  background: #059669;
}

.undo-button {
  background: #6b7280;
  color: white;
}

.undo-button:hover {
  background: #4b5563;
}

.waiting-button {
  background: #9ca3af;
  color: white;
  cursor: not-allowed;
}

.delete-button {
  background: #ef4444;
  color: white;
}

.delete-button:hover {
  background: #dc2626;
}

.button-icon {
  width: 14px;
  height: 14px;
}

.play-icon {
  width: 14px;
  height: 14px;
}

/* Task Description */
.task-description {
  font-size: 14px;
  color: #374151;
  margin-bottom: 16px;
  line-height: 1.5;
  font-weight: 500;
}

/* Task Info Section */
.task-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
}

.info-icon {
  width: 16px;
  height: 16px;
  color: #6b7280;
}

.assignee-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.assignee-avatar {
  width: 24px;
  height: 24px;
  background: #f97316;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text {
  color: white;
  font-weight: 600;
  font-size: 12px;
}

.assignee-name {
  font-size: 13px;
  color: #374151;
  font-weight: 500;
}

.time-info {
  display: flex;
  align-items: center;
}

.time-value {
  font-size: 13px;
  color: #374151;
  font-weight: 500;
}

/* Dependencies Section */
.task-dependencies {
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
}

.dependency-title {
  font-size: 13px;
  color: #374151;
  margin-bottom: 12px;
  font-weight: 600;
}

.dependency-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dependency-row {
  display: flex;
  align-items: center;
  gap: 12px;
}

.dependency-label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  min-width: 80px;
}

.dependency-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  flex: 1;
}

.dependency-tag {
  background: #f3f4f6;
  color: #374151;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid #e5e7eb;
} 