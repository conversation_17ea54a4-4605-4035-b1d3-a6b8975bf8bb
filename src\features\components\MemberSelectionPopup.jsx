import { useState, useEffect } from "react";
import "../../styles/MemberAddPopup.css";

const MemberSelectionPopup = ({
  isOpen,
  onClose,
  onAddMember,
  existingMembers = [],
  users = [],
  loadingUsers = false,
  departmentId,
  filterByDepartment = false,
  singleSelection = false, // Thêm prop để giới hạn chọn 1 thành viên
}) => {
  const [searchEmail, setSearchEmail] = useState("");
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [error, setError] = useState("");

  // Lọc users theo các điều kiện:
  // 1. Lọc theo departmentId (nếu có và filterByDepartment = true)
  // 2. Chỉ lấy users có role 'staff' (nếu filterByDepartment = true)
  // 3. Loại trừ những người đã được chọn
  useEffect(() => {
    const filtered = users.filter((user) => {
      // Lọc theo department nếu cần
      if (
        filterByDepartment &&
        departmentId &&
        user.departmentId !== departmentId
      ) {
        return false;
      }

      // Chỉ lấy staff (cho project creation)
      if (filterByDepartment) {
        const userRole = (user.role || "").toLowerCase();
        if (userRole !== "staff") {
          return false;
        }
      }

      // Loại trừ những người đã được chọn
      if (existingMembers.some(member => member.id === user.id)) {
        return false;
      }

      // Lọc theo email nếu có nhập
      if (searchEmail.trim()) {
        const searchTerm = searchEmail.toLowerCase();
        return (
          (user.email && user.email.toLowerCase().includes(searchTerm)) ||
          (user.name && user.name.toLowerCase().includes(searchTerm))
        );
      }

      return true;
    });

    setFilteredUsers(filtered);
  }, [users, departmentId, filterByDepartment, existingMembers, searchEmail]);

  const handleSearchChange = (value) => {
    setSearchEmail(value);
    setError("");
  };

  const handleSelectMember = (user) => {
    onAddMember(user);
    setSearchEmail("");
    setError("");
  };

  const handleClose = () => {
    setSearchEmail("");
    setError("");
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="member-add-overlay">
      <div className="member-add-popup" style={{ maxWidth: '600px', width: '90%' }}>
        <div className="member-add-header">
          <h3>{singleSelection ? "Chọn thành viên thực hiện" : "Chọn thành viên"}</h3>
          <button className="member-add-close-btn" onClick={handleClose}>
            ×
          </button>
        </div>

        <div className="member-add-form">
          <div className="member-add-form-group">
            <label>Tìm kiếm thành viên</label>
            <div className="member-add-input-container">
              <input
                type="text"
                placeholder={
                  loadingUsers
                    ? "Đang tải danh sách thành viên..."
                    : "Nhập email hoặc tên để tìm kiếm..."
                }
                value={searchEmail}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="member-add-input"
                autoComplete="off"
                disabled={loadingUsers}
              />
            </div>
            {error && <div className="member-add-error">{error}</div>}
          </div>

          {singleSelection && (
            <div style={{ 
              fontSize: "12px", 
              color: "#666", 
              marginTop: "8px",
              padding: "8px",
              backgroundColor: "#f8f9fa",
              borderRadius: "4px",
              border: "1px solid #e9ecef"
            }}>
              Chỉ có thể chọn 1 thành viên từ danh sách thành viên của công việc chính
            </div>
          )}

          <div className="member-selection-list" style={{ 
            maxHeight: '400px', 
            overflowY: 'auto',
            border: '1px solid #e0e0e0',
            borderRadius: '6px',
            marginTop: '16px'
          }}>
            {loadingUsers ? (
              <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
                Đang tải danh sách thành viên...
              </div>
            ) : filteredUsers.length === 0 ? (
              <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
                {searchEmail.trim() ? 'Không tìm thấy thành viên phù hợp' : 'Không có thành viên nào khả dụng'}
              </div>
            ) : (
              <div>
                {filteredUsers.map((user, index) => (
                  <div
                    key={user.id || index}
                    className="member-add-suggestion-item"
                    onClick={() => handleSelectMember(user)}
                    style={{
                      padding: '12px 16px',
                      borderBottom: '1px solid #f0f0f0',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px'
                    }}
                  >
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="suggestion-avatar"
                      style={{ width: '40px', height: '40px', borderRadius: '50%' }}
                    />
                    <div className="suggestion-info" style={{ flex: 1 }}>
                      <div className="suggestion-name" style={{ 
                        fontWeight: '500', 
                        color: '#333',
                        marginBottom: '4px'
                      }}>
                        {user.name}
                      </div>
                      <div className="suggestion-email" style={{ 
                        fontSize: '14px', 
                        color: '#666',
                        marginBottom: '2px'
                      }}>
                        {user.email}
                      </div>
                      <div className="suggestion-department" style={{ 
                        fontSize: '12px', 
                        color: '#999' 
                      }}>
                        {user.department} - {user.role}
                      </div>
                    </div>
                    <div style={{ 
                      color: '#007bff', 
                      fontSize: '14px',
                      fontWeight: '500'
                    }}>
                      Chọn
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="member-add-actions" style={{ marginTop: '16px' }}>
            <button
              type="button"
              className="member-add-cancel-btn"
              onClick={handleClose}
            >
              Đóng
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MemberSelectionPopup; 